import logging
from django.core.management.base import BaseCommand
from vantedge.users.models import Company
from vantedge.iot.models import EventType

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Load predefined event types for the IoT module"

    def handle(self, *args, **options):
        EventType.objects.all().delete()
        # Create Truck event type
        truck_event_type = EventType.objects.create(
            name="Truck",
            code="TRUCK",
            description="Truck operations",
            is_active=True,
            display_order=10,
        )

        # Create Altagas Alert event
        truck_event_type = EventType.objects.create(
            company=Company.objects.get(name="AltaGas"),
            name="AltaGas Alert",
            description="Specific event type for a company",
            is_active=True,
            display_order=10,
        )

        # Create Steelreef Alert event
        truck_event_type = EventType.objects.create(
            company=Company.objects.get(name="SteelReef"),
            name="SteelReef Alert",
            description="Specific event type for a company",
            is_active=True,
            display_order=10,
        )

        self.stdout.write(self.style.SUCCESS("Successfully loaded event types"))
