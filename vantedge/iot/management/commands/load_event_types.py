import csv
import logging
import os
from django.core.management.base import BaseCommand
from vantedge.iot.models import EventType

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Load predefined event types for the IoT module from CSV file"

    def add_arguments(self, parser):
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear all existing event types before loading new ones",
        )

    def handle(self, *args, **options):
        # Get the directory where this command file is located
        command_dir = os.path.dirname(os.path.abspath(__file__))
        csv_file_path = os.path.join(command_dir, "../../seed/event_types.csv")

        if not os.path.exists(csv_file_path):
            self.stdout.write(self.style.ERROR(f"CSV file not found: {csv_file_path}"))
            return

        # Clear existing event types if requested
        if options["clear"]:
            deleted_count = EventType.objects.count()
            EventType.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f"Deleted {deleted_count} existing event types")
            )

        # Dictionary to store created event types for parent lookup
        created_event_types = {}

        # Read and process CSV file - single pass, top to bottom
        try:
            with open(csv_file_path, "r", newline="", encoding="utf-8") as csvfile:
                reader = csv.DictReader(
                    csvfile
                )  # This automatically skips the header row

                for row_num, row in enumerate(
                    reader, start=2
                ):  # Start at 2 because of header
                    try:
                        # Validate required fields
                        required_fields = [
                            "name",
                            "code",
                            "description",
                            "is_active",
                            "display_order",
                            "max_events_per_location",
                            "retention_days",
                        ]
                        for field in required_fields:
                            if not row.get(field, "").strip():
                                raise ValueError(
                                    f"Required field '{field}' is missing or empty"
                                )

                        parent = None
                        if row["parent_code"].strip():
                            parent_code = row["parent_code"].strip()
                            parent = created_event_types.get(parent_code)

                            if not parent:
                                # Try to find existing parent in database
                                try:
                                    parent = EventType.objects.get(
                                        code=parent_code, company=None
                                    )
                                    created_event_types[parent_code] = parent
                                except EventType.DoesNotExist:
                                    self.stdout.write(
                                        self.style.ERROR(
                                            f'Row {row_num}: Parent event type with code "{parent_code}" not found for {row["name"]}'
                                        )
                                    )
                                    continue

                        event_type, created = self._create_or_update_event_type(
                            row, parent
                        )
                        created_event_types[row["code"].strip()] = event_type

                        action = "Created" if created else "Updated"
                        if parent:
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"{action} child event type: {event_type.name} (parent: {parent.name})"
                                )
                            )
                        else:
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f"{action} parent event type: {event_type.name}"
                                )
                            )

                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(
                                f"Row {row_num}: Error processing {row.get('name', 'unknown')}: {str(e)}"
                            )
                        )
                        continue

        except FileNotFoundError:
            self.stdout.write(self.style.ERROR(f"CSV file not found: {csv_file_path}"))
            return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading CSV file: {str(e)}"))
            return

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully processed {len(created_event_types)} event types"
            )
        )

    def _create_or_update_event_type(self, row, parent):
        """Create or update an EventType from CSV row data."""
        code = row["code"].strip()

        # Prepare the data for create/update
        defaults = {
            "name": row["name"].strip(),
            "description": row["description"].strip(),
            "parent": parent,
            "is_active": row["is_active"].strip().lower() == "true",
            "display_order": int(row["display_order"].strip()),
            "max_events_per_location": int(row["max_events_per_location"].strip()),
            "retention_days": int(row["retention_days"].strip()),
        }

        # Use get_or_create for global event types (company=None)
        event_type, created = EventType.objects.get_or_create(
            code=code,
            company=None,  # All event types are global
            defaults=defaults,
        )

        # If it exists but we want to update it, update the fields
        if not created:
            for field, value in defaults.items():
                setattr(event_type, field, value)
            event_type.save()

        return event_type, created
