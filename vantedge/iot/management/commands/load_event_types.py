import csv
import logging
import os
from django.core.management.base import BaseCommand
from vantedge.iot.models import EventType

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Load predefined event types for the IoT module from CSV file"

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear all existing event types before loading new ones',
        )

    def handle(self, *args, **options):
        # Get the directory where this command file is located
        command_dir = os.path.dirname(os.path.abspath(__file__))
        csv_file_path = os.path.join(command_dir, 'event_types.csv')

        if not os.path.exists(csv_file_path):
            self.stdout.write(
                self.style.ERROR(f'CSV file not found: {csv_file_path}')
            )
            return

        # Clear existing event types if requested
        if options['clear']:
            deleted_count = EventType.objects.count()
            EventType.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {deleted_count} existing event types')
            )

        # Dictionary to store created event types for parent lookup
        created_event_types = {}

        # Read and process CSV file - single pass, top to bottom
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)  # This automatically skips the header row

            for row in reader:
                parent = None
                if row['parent_code'].strip():
                    parent_code = row['parent_code'].strip()
                    parent = created_event_types.get(parent_code)

                    if not parent:
                        self.stdout.write(
                            self.style.ERROR(
                                f'Parent event type with code "{parent_code}" not found for {row["name"]}'
                            )
                        )
                        continue

                event_type = self._create_event_type(row, parent)
                created_event_types[row['code']] = event_type

                if parent:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Created child event type: {event_type.name} (parent: {parent.name})'
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'Created parent event type: {event_type.name}')
                    )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully loaded {len(created_event_types)} event types')
        )

    def _create_event_type(self, row, parent):
        """Create an EventType from CSV row data."""
        return EventType.objects.create(
            name=row['name'].strip(),
            code=row['code'].strip(),
            description=row['description'].strip(),
            parent=parent,
            is_active=row['is_active'].strip().lower() == 'true',
            display_order=int(row['display_order'].strip()),
            max_events_per_location=int(row['max_events_per_location'].strip()),
            retention_days=int(row['retention_days'].strip()),
        )
