import csv
import os
from django.core.management.base import BaseCommand
from vantedge.iot.models import EventType
from vant_dbreplication.signals.log_entry_signal import no_sync_signals


class Command(BaseCommand):
    help = "Load predefined event types for the IoT module from CSV file"

    def add_arguments(self, parser):
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear all existing event types before loading new ones",
        )

    def handle(self, *args, **options):
        command_dir = os.path.dirname(os.path.abspath(__file__))
        csv_file_path = os.path.join(command_dir, "../../seed/event_types.csv")
        if options["clear"]:
            EventType.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared existing event types"))

        created_event_types = {}

        with open(csv_file_path, "r") as csvfile:
            reader = csv.DictReader(csvfile)

            for row in reader:
                parent = None
                if row["parent_code"]:
                    parent = created_event_types.get(row["parent_code"])

                event_type, created = EventType.objects.get_or_create(
                    code=row["code"],
                    company=None,
                    defaults={
                        "name": row["name"],
                        "description": row["description"],
                        "parent": parent,
                        "is_active": row["is_active"].lower() == "true",
                        "display_order": int(row["display_order"]),
                        "max_events_per_location": int(row["max_events_per_location"]),
                        "retention_days": int(row["retention_days"]),
                    },
                )

                created_event_types[row["code"]] = event_type
                action = "Created" if created else "Found existing"
                self.stdout.write(f"{action}: {event_type.name}")

        self.stdout.write(
            self.style.SUCCESS(f"Loaded {len(created_event_types)} event types")
        )
